/**
 * React hooks for retry functionality
 * Provides easy-to-use retry mechanisms for React components
 */

import { useState, useCallback, useRef } from 'react';
import { RetryMechanism, RetryConfig, DEFAULT_RETRY_CONFIGS } from '@/lib/retry-mechanism';

export interface UseRetryState {
  isRetrying: boolean;
  retryCount: number;
  lastError: any;
  canRetry: boolean;
}

export interface UseRetryOptions {
  maxAttempts?: number;
  operationType?: keyof typeof DEFAULT_RETRY_CONFIGS;
  config?: Partial<RetryConfig>;
  onRetry?: (attempt: number, error: any) => void;
  onSuccess?: (data: any, attempts: number) => void;
  onFailure?: (error: any, attempts: number) => void;
  onMaxAttemptsReached?: (error: any) => void;
}

/**
 * Hook for adding retry functionality to async operations
 */
export function useRetry<T>(
  operation: () => Promise<T>,
  options: UseRetryOptions = {}
) {
  const [state, setState] = useState<UseRetryState>({
    isRetrying: false,
    retryCount: 0,
    lastError: null,
    canRetry: true
  });

  const retryMechanismRef = useRef<RetryMechanism>();

  // Initialize retry mechanism
  if (!retryMechanismRef.current) {
    retryMechanismRef.current = new RetryMechanism({
      maxAttempts: options.maxAttempts || 3,
      ...options.config,
      onRetry: (attempt, error) => {
        setState(prev => ({
          ...prev,
          retryCount: attempt,
          lastError: error,
          isRetrying: true
        }));
        options.onRetry?.(attempt, error);
      },
      onMaxAttemptsReached: (error) => {
        setState(prev => ({
          ...prev,
          canRetry: false,
          isRetrying: false,
          lastError: error
        }));
        options.onMaxAttemptsReached?.(error);
      }
    });
  }

  const execute = useCallback(async (): Promise<T> => {
    setState(prev => ({
      ...prev,
      isRetrying: true,
      lastError: null
    }));

    try {
      const result = await retryMechanismRef.current!.execute(
        operation,
        options.operationType
      );

      if (result.success) {
        setState(prev => ({
          ...prev,
          isRetrying: false,
          retryCount: result.attempts,
          canRetry: true
        }));
        options.onSuccess?.(result.data, result.attempts);
        return result.data!;
      } else {
        setState(prev => ({
          ...prev,
          isRetrying: false,
          lastError: result.error,
          retryCount: result.attempts,
          canRetry: false
        }));
        options.onFailure?.(result.error, result.attempts);
        throw result.error;
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        isRetrying: false,
        lastError: error,
        canRetry: false
      }));
      throw error;
    }
  }, [operation, options]);

  const reset = useCallback(() => {
    setState({
      isRetrying: false,
      retryCount: 0,
      lastError: null,
      canRetry: true
    });
  }, []);

  return {
    execute,
    reset,
    ...state
  };
}

/**
 * Hook for retrying API calls
 */
export function useApiRetry<T>(
  apiCall: () => Promise<T>,
  options: Omit<UseRetryOptions, 'operationType'> = {}
) {
  return useRetry(apiCall, {
    ...options,
    operationType: 'api'
  });
}

/**
 * Hook for retrying network requests
 */
export function useNetworkRetry<T>(
  networkRequest: () => Promise<T>,
  options: Omit<UseRetryOptions, 'operationType'> = {}
) {
  return useRetry(networkRequest, {
    ...options,
    operationType: 'network'
  });
}

/**
 * Hook for retrying user operations
 */
export function useUserOperationRetry<T>(
  userOperation: () => Promise<T>,
  options: Omit<UseRetryOptions, 'operationType'> = {}
) {
  return useRetry(userOperation, {
    ...options,
    operationType: 'user'
  });
}

/**
 * Hook for automatic retry with exponential backoff
 */
export function useAutoRetry<T>(
  operation: () => Promise<T>,
  options: UseRetryOptions & {
    autoRetry?: boolean;
    retryDelay?: number;
  } = {}
) {
  const [autoRetryEnabled, setAutoRetryEnabled] = useState(options.autoRetry ?? false);
  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  const retry = useRetry(operation, {
    ...options,
    onFailure: (error, attempts) => {
      options.onFailure?.(error, attempts);
      
      // Schedule automatic retry if enabled and we haven't reached max attempts
      if (autoRetryEnabled && attempts < (options.maxAttempts || 3)) {
        const delay = options.retryDelay || 2000;
        retryTimeoutRef.current = setTimeout(() => {
          retry.execute().catch(() => {
            // Error already handled by retry mechanism
          });
        }, delay);
      }
    }
  });

  const enableAutoRetry = useCallback(() => {
    setAutoRetryEnabled(true);
  }, []);

  const disableAutoRetry = useCallback(() => {
    setAutoRetryEnabled(false);
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
  }, []);

  const executeWithAutoRetry = useCallback(async (): Promise<T> => {
    try {
      return await retry.execute();
    } catch (error) {
      // If auto-retry is enabled, the retry will be scheduled automatically
      throw error;
    }
  }, [retry]);

  return {
    ...retry,
    execute: executeWithAutoRetry,
    autoRetryEnabled,
    enableAutoRetry,
    disableAutoRetry
  };
}

/**
 * Hook for retry with user confirmation
 */
export function useConfirmRetry<T>(
  operation: () => Promise<T>,
  options: UseRetryOptions & {
    confirmMessage?: string;
    autoConfirm?: boolean;
  } = {}
) {
  const [pendingRetry, setPendingRetry] = useState<{
    attempt: number;
    error: any;
    resolve: (value: boolean) => void;
  } | null>(null);

  const retry = useRetry(operation, {
    ...options,
    onRetry: (attempt, error) => {
      options.onRetry?.(attempt, error);
      
      // If auto-confirm is disabled, ask for user confirmation
      if (!options.autoConfirm && attempt > 1) {
        return new Promise<void>((resolve) => {
          setPendingRetry({
            attempt,
            error,
            resolve: (confirmed: boolean) => {
              setPendingRetry(null);
              if (confirmed) {
                resolve();
              } else {
                throw new Error('Retry cancelled by user');
              }
            }
          });
        });
      }
    }
  });

  const confirmRetry = useCallback(() => {
    if (pendingRetry) {
      pendingRetry.resolve(true);
    }
  }, [pendingRetry]);

  const cancelRetry = useCallback(() => {
    if (pendingRetry) {
      pendingRetry.resolve(false);
    }
  }, [pendingRetry]);

  return {
    ...retry,
    pendingRetry,
    confirmRetry,
    cancelRetry
  };
}

/**
 * Hook for batch retry operations
 */
export function useBatchRetry<T>(
  operations: (() => Promise<T>)[],
  options: UseRetryOptions = {}
) {
  const [batchState, setBatchState] = useState({
    isRetrying: false,
    completedCount: 0,
    failedCount: 0,
    results: [] as T[],
    errors: [] as any[]
  });

  const executeBatch = useCallback(async (): Promise<T[]> => {
    setBatchState({
      isRetrying: true,
      completedCount: 0,
      failedCount: 0,
      results: [],
      errors: []
    });

    const results: T[] = [];
    const errors: any[] = [];

    for (let i = 0; i < operations.length; i++) {
      try {
        const retryMechanism = new RetryMechanism(options.config);
        const result = await retryMechanism.execute(operations[i], options.operationType);
        
        if (result.success) {
          results.push(result.data!);
          setBatchState(prev => ({
            ...prev,
            completedCount: prev.completedCount + 1,
            results: [...prev.results, result.data!]
          }));
        } else {
          errors.push(result.error);
          setBatchState(prev => ({
            ...prev,
            failedCount: prev.failedCount + 1,
            errors: [...prev.errors, result.error]
          }));
        }
      } catch (error) {
        errors.push(error);
        setBatchState(prev => ({
          ...prev,
          failedCount: prev.failedCount + 1,
          errors: [...prev.errors, error]
        }));
      }
    }

    setBatchState(prev => ({
      ...prev,
      isRetrying: false
    }));

    if (errors.length > 0) {
      throw new Error(`${errors.length} operations failed out of ${operations.length}`);
    }

    return results;
  }, [operations, options]);

  return {
    executeBatch,
    ...batchState
  };
}
